import React from 'react';
import Svg, { Path } from 'react-native-svg';

const WhatsAppIcon = (props) => (
    <Svg 
        width={props.width || "20"} 
        height={props.height || "20"} 
        viewBox="0 0 24 24" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
    >
        <Path
            d="M17.472 6.178a5.825 5.825 0 00-4.131-1.711H12c-3.219 0-5.838 2.619-5.838 5.838 0 1.031.269 2.044.781 2.931L6 17l3.862-1.013a5.785 5.785 0 002.794.719h.003c3.219 0 5.838-2.619 5.838-5.838a5.825 5.825 0 00-1.711-4.131l-.314-.559zM12.659 15.394h-.003a4.817 4.817 0 01-2.458-.672l-.176-.105-1.825.478.487-1.781-.115-.183a4.832 4.832 0 01-.741-2.578c0-2.663 2.169-4.831 4.834-4.831a4.811 4.811 0 013.406 1.412 4.811 4.811 0 011.412 3.406c-.003 2.663-2.172 4.831-4.834 4.831l.013.023z"
            fill={props.color || '#25D366'}
        />
        <Path
            d="M15.456 13.591c-.144-.072-.853-.422-1.006-.469-.144-.047-.25-.072-.356.072-.106.144-.409.469-.5.566-.094.097-.188.109-.331.037-.144-.072-.606-.225-1.156-.716-.428-.381-.716-.853-.8-.997-.084-.144-.009-.222.063-.294.066-.063.144-.166.216-.25.072-.084.097-.144.144-.241.047-.097.022-.181-.013-.253-.034-.072-.356-.859-.488-1.175-.128-.306-.259-.266-.356-.269-.091-.003-.194-.003-.297-.003a.571.571 0 00-.413.194c-.144.159-.544.531-.544 1.294s.556 1.5.634 1.603c.078.103 1.1 1.681 2.663 2.356.372.162.663.259.891.331.375.119.716.103.984.063.3-.044.925-.378 1.056-.744.131-.366.131-.681.094-.744-.038-.063-.141-.103-.294-.175z"
            fill={props.color || '#25D366'}
        />
    </Svg>
);

export default WhatsAppIcon;
